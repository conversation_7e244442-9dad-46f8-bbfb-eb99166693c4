import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development'
  // 开发态通过本地代理，避免浏览器直连跨域
  const apiBase = isDev ? '/api/' : ''

  return {
    plugins: [react()],
    define: {
      'import.meta.env.VITE_API_BASE': JSON.stringify(apiBase),
    },
    server: {
      host: '127.0.0.1',
      port: 5173,
      allowedHosts: ['al17993272y.vicp.fun'],
      proxy: {
        '/api': {
          target: 'http://*************:8091/conceptual/',
          changeOrigin: true,
          // 去掉前缀 /api
          rewrite: (path) => path.replace(/^\/api\/?/, ''),
        },
      },
    },
  }
})
