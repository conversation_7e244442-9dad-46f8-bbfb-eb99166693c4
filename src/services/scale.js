import { apiBaseUrl } from '../config/env'

/**
 * 获取题目列表
 * @param {Object} params - 请求参数
 * @param {string} params.projectId - 项目ID
 * @returns {Promise} API响应
 */
export const getQuestionItemList = async (params) => {
  try {
    const response = await fetch(`${apiBaseUrl}scale/v1.1/getQuestionItemList`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'sourceType': 'H5'
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('获取题目列表失败:', error)
    throw error
  }
}

/**
 * 提交问卷数据
 * @param {Object} params - 请求参数
 * @param {string} params.projectId - 项目ID
 * @param {string} params.workorderId - 工单ID
 * @param {string} params.traineeId - 患者ID
 * @param {Array} params.serNumList - 题目序号列表
 * @param {Array} params.answerList - 答案列表
 * @param {string} params.evaluationSourceType - 评估来源类型
 * @returns {Promise} API响应
 */
export const subQuestionData = async (params) => {
  try {
    const response = await fetch(`${apiBaseUrl}scale/v1.1/subQuestionData`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'sourceType': 'H5'
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    
    if (data.code !== '0000') {
      throw new Error(data.msg || '提交失败')
    }
    
    return data
  } catch (error) {
    console.error('提交问卷数据失败:', error)
    throw error
  }
}

/**
 * 显示提示信息
 * @param {string} message - 提示信息
 * @param {string} type - 提示类型 ('success', 'error', 'warning', 'info')
 */
export const showToast = (message, type = 'info') => {
  // 简单的提示实现，可以根据需要替换为更复杂的提示组件
  console.log(`[${type.toUpperCase()}] ${message}`)
  
  // 如果在浏览器环境中，可以使用alert作为临时方案
  if (typeof window !== 'undefined') {
    alert(message)
  }
}
