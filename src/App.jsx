import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import './App.css'
import Guide from './pages/Guide'
import Assessment from './pages/Assessment'

export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Guide />} />
        <Route path="/assessment" element={<Assessment />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </BrowserRouter>
  )
}
