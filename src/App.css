/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Alibaba PuHuiTi', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #ffffff;
  overflow-x: hidden;
}

.app {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;
  margin: 0;
  padding: 0;
}

/* 状态栏样式已移除 */

/* 标题栏 */
.header {
  width: 100%;
  height: 88px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-background {
  width: 100%;
  height: 88px;
  background-color: #287FFF;
  position: absolute;
  top: 0;
  left: 0;
}

/* back icon 已移除 */

.header-title {
  font-family: 'Alibaba PuHuiTi';
  font-size: 40px;
  font-weight: 400;
  line-height: 54.88px;
  color: #ffffff;
  text-align: center;
  z-index: 1;
}

/* 顶部标签栏 */
.top-tabs {
  width: 100%;
  height: 88px;
  display: flex;
  position: relative;
}

.tab {
  flex: 1;
  height: 88px;
  background-color: #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px 20px;
  cursor: pointer;
}

.tab.active {
  background-color: #EEEEEE;
}

.tab span {
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  text-align: center;
}

.tab.active span {
  color: #287FFF;
}

.tab:not(.active) span {
  color: #666666;
}

.tab-indicator {
  width: 120px;
  height: 4.51px;
  background-color: #287FFF;
  border-radius: 4px;
  position: absolute;
  bottom: 0;
  left: calc(25% - 60px);
  transition: left 0.25s ease;
}

.tab-indicator.second {
  left: calc(75% - 60px);
}

/* 教师部分徽标按钮 */
.badge-done {
  padding: 12px 20px;
  border: none;
  border-radius: 24px;
  background-color: #287FFF;
  color: #ffffff;
  font-family: 'Alibaba PuHuiTi';
  font-size: 24px;
  line-height: 24px;
  letter-spacing: -0.6px;
  white-space: nowrap;
}

.badge-todo {
  padding: 12px 20px;
  border: none;
  border-radius: 24px;
  background-color: #FFB130;
  color: #ffffff;
  font-family: 'Alibaba PuHuiTi';
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 1px;
  white-space: nowrap;
}

/* 资料信息区域 */
.info-section {
  padding: 40px 24px 64px 24px;
  width: 100%;
}

.hospital-info {
  font-family: 'Alibaba PuHuiTi';
  font-size: 32px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  color: #333333;
  margin-bottom: 24px;
}

.patient-info {
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  color: #666666;
  margin-bottom: 24px;
}

.validity-info {
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  color: #FFB130;
}

/* 测评项目列表 */
.assessment-list {
  padding: 0 24px;
  width: 100%;
}

.assessment-item {
  width: 100%;
  border: 1px solid #EEEEEE;
  border-radius: 16px;
  padding: 32px 24px;
  margin-bottom: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  background-color: #ffffff;
}

.assessment-item.completed {
  position: relative;
}

.completed-badge {
  width: 24px;
  height: 24px;
  background-color: #EBF3FF;
  border-radius: 16px;
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assessment-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 20px;
}

.assessment-title {
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 42px;
  letter-spacing: -0.6px;
  color: #333333;
  text-align: left;
}

.assessment-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-shrink: 0;
}

.detail-btn {
  padding: 16px 24px;
  border: 1px solid #287FFF;
  border-radius: 32px;
  background-color: transparent;
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: -0.6px;
  color: #287FFF;
  cursor: pointer;
  white-space: nowrap;
}

.retest-btn, .start-btn {
  padding: 16px 24px;
  border: none;
  border-radius: 32px;
  background-color: #287FFF;
  font-family: 'Alibaba PuHuiTi';
  font-size: 28px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 1px;
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
}

.detail-btn:hover {
  background-color: #f5f5f5;
}

.retest-btn:hover, .start-btn:hover {
  background-color: #1e6dd8;
}

/* 底部提示 */
.bottom-tip {
  /* 作为普通文档流元素显示在列表之后 */
  position: static;
  margin: 24px 0 40px;
  padding: 0 24px;
  font-family: 'Alibaba PuHuiTi';
  font-size: 24px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: -0.6px;
  color: #999999;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .info-section {
    padding: 30px 16px 40px 16px;
  }
  
  .assessment-list {
    padding: 0 16px;
  }
  
  .assessment-item {
    width: 100%;
    padding: 24px 16px;
  }
  
  .assessment-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .assessment-buttons {
    width: 100%;
    justify-content: flex-end;
  }
  
  .tab {
    padding: 24px 10px;
  }
  
  .tab span {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 32px;
  }
  
  .hospital-info {
    font-size: 28px;
  }
  
  .patient-info, .validity-info {
    font-size: 24px;
  }
  
  .assessment-title {
    font-size: 24px;
    line-height: 36px;
  }
  
  .detail-btn, .retest-btn, .start-btn {
    font-size: 24px;
    padding: 12px 20px;
  }
  
  .bottom-tip {
    font-size: 20px;
  }
}
