/* 测评问卷页面样式 */
.questionnaire-page {
  width: 100vw;
  height: 100vh;
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}

/* 顶部导航栏 */
.questionnaire-header {
  position: relative;
  width: 100%;
  height: 82px;
  background: #ffffff;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn {
  position: absolute;
  left: 37px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.questionnaire-title {
  font-family: 'Alibaba PuHuiTi', 'PingFang SC', sans-serif;
  font-size: 30px;
  color: #333333;
  line-height: 30px;
  font-weight: 600;
}

/* 进度条区域 */
.progress-section {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.progress-bar {
  width: 90%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #41a8de;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
  margin-top: 20px;
}

.current-number {
  font-size: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #111111;
}

/* 题目内容区域 */
.question-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  flex: 1;
  padding: 0 24px;
}

.question-instruction {
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
  margin-bottom: 20px;
}

.question-loading,
.question-empty {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
}

.question-container {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.question-text {
  font-size: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #111111;
  line-height: 28px;
  min-height: 64px;
  display: flex;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

.options-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  width: 100%;
  height: 60px;
  background: #f6f6f6;
  border-radius: 30px;
  text-align: center;
  line-height: 60px;
  color: #111111;
  font-size: 18px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: #e8f4fd;
}

.option-item.selected {
  background: #41a8de;
  color: #ffffff;
}

/* 底部按钮区域 */
.bottom-section {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 0 24px;
}

.previous-button {
  width: 100%;
  max-width: 255px;
  height: 50px;
  background: #f6f6f6;
  border-radius: 30px;
  border: 1px solid #41a8de;
  font-size: 18px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #41a8de;
  cursor: pointer;
  transition: all 0.3s ease;
}

.previous-button:hover {
  background: #e8f4fd;
}
