/* 测评问卷页面样式 */
.questionnaire-page {
  width: 100vw;
  height: 100vh;
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}

/* 顶部导航栏 */
.questionnaire-header {
  position: relative;
  width: 100%;
  height: 82px;
  background: #ffffff;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn {
  position: absolute;
  left: 37px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.questionnaire-title {
  font-family: 'Alibaba PuHuiTi', 'PingFang SC', sans-serif;
  font-size: 30px;
  color: #333333;
  line-height: 30px;
  font-weight: 600;
}

/* 进度条区域 */
.progress-section {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.progress-bar {
  width: 90%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #41a8de;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
  margin-top: 20px;
}

.current-number {
  font-size: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #111111;
}

/* 题目内容区域 */
.question-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  flex: 1;
  padding: 0 24px;
}

.question-instruction {
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
  margin-bottom: 20px;
}

.question-loading,
.question-empty {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #999999;
}

.question-container {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.question-text {
  font-size: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #111111;
  line-height: 28px;
  min-height: 64px;
  display: flex;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

.options-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  width: 100%;
  height: 60px;
  background: #f6f6f6;
  border-radius: 30px;
  text-align: center;
  line-height: 60px;
  color: #111111;
  font-size: 18px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: #e8f4fd;
}

.option-item.selected {
  background: #41a8de;
  color: #ffffff;
}

/* 底部按钮区域 */
.bottom-section {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 0 24px;
}

.previous-button {
  width: 100%;
  max-width: 255px;
  height: 50px;
  background: #f6f6f6;
  border-radius: 30px;
  border: 1px solid #41a8de;
  font-size: 18px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #41a8de;
  cursor: pointer;
  transition: all 0.3s ease;
}

.previous-button:hover {
  background: #e8f4fd;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  max-width: 370px;
  background: #ffffff;
  border-radius: 10px;
  padding: 0 20px;
}

.modal-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.modal-title {
  font-size: 24px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  color: #111111;
  margin-bottom: 40px;
  margin-top: 40px;
}

.modal-body {
  font-size: 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  color: #666666;
  line-height: 24px;
  margin-bottom: 36px;
  text-align: center;
}

.modal-footer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-button-cancel {
  width: 130px;
  height: 60px;
  background: rgba(65, 168, 222, 0.1);
  border-radius: 12px;
  border: none;
  font-size: 20px;
  font-weight: 600;
  color: #41a8de;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.modal-button-cancel:hover {
  background: rgba(65, 168, 222, 0.2);
}

.modal-button-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-button-confirm {
  width: 130px;
  height: 60px;
  background: #41a8de;
  border-radius: 12px;
  border: none;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.modal-button-confirm:hover {
  background: #3a96c7;
}

.modal-button-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-button-single {
  width: 280px;
  height: 60px;
  background: #41a8de;
  border-radius: 12px;
  border: none;
  font-size: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  margin: 0 auto;
  transition: background-color 0.3s ease;
}

.modal-button-single:hover {
  background: #3a96c7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionnaire-header {
    height: 70px;
  }

  .questionnaire-title {
    font-size: 26px;
  }

  .question-content {
    padding: 0 16px;
  }

  .question-text {
    font-size: 18px;
    line-height: 26px;
    min-height: 52px;
  }

  .option-item {
    height: 55px;
    line-height: 55px;
    font-size: 16px;
  }

  .bottom-section {
    padding: 0 16px;
  }

  .previous-button {
    height: 45px;
    font-size: 16px;
  }

  .modal-content {
    width: 95%;
    max-width: 320px;
  }

  .modal-title {
    font-size: 20px;
  }

  .modal-body {
    font-size: 14px;
    line-height: 20px;
  }

  .modal-button-cancel,
  .modal-button-confirm {
    width: 110px;
    height: 50px;
    font-size: 18px;
  }

  .modal-button-single {
    width: 240px;
    height: 50px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .questionnaire-header {
    height: 60px;
  }

  .questionnaire-title {
    font-size: 22px;
  }

  .question-text {
    font-size: 16px;
    line-height: 24px;
    min-height: 48px;
  }

  .option-item {
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }

  .previous-button {
    height: 40px;
    font-size: 14px;
  }

  .modal-title {
    font-size: 18px;
  }

  .modal-body {
    font-size: 12px;
    line-height: 18px;
  }

  .modal-button-cancel,
  .modal-button-confirm {
    width: 90px;
    height: 45px;
    font-size: 16px;
  }

  .modal-button-single {
    width: 200px;
    height: 45px;
    font-size: 16px;
  }
}
