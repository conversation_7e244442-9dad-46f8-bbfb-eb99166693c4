import { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { getQuestionItemList, subQuestionData, showToast } from '../services/scale'
import './AssessmentQuestionnaire.css'

export default function AssessmentQuestionnaire() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  
  // 获取URL参数
  const projectId = searchParams.get('projectId') || ''
  const projectName = searchParams.get('projectName') || 'SNAP-IV父母评定问卷'
  const workOrderId = searchParams.get('workOrderId') || ''
  const traineeId = searchParams.get('traineeId') || ''
  
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [questions, setQuestions] = useState([])
  const [options, setOptions] = useState([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(1)
  const [selectedAnswer, setSelectedAnswer] = useState('')
  const [answers, setAnswers] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSubmitModal, setShowSubmitModal] = useState(false)
  const [showGuideModal, setShowGuideModal] = useState(true)
  
  // 计算属性
  const totalQuestions = questions.length
  const progressPercent = totalQuestions > 0 ? (currentQuestionIndex / totalQuestions) * 100 : 0
  const currentQuestion = questions[currentQuestionIndex - 1] || {}
  const showPreviousButton = currentQuestionIndex > 1

  // 加载题目列表
  useEffect(() => {
    if (projectId) {
      loadQuestionItems()
    }
  }, [projectId])

  const loadQuestionItems = async () => {
    setLoading(true)
    try {
      const response = await getQuestionItemList({ projectId })
      
      if (response.code === '0000' && response.data && response.data.itemList) {
        setQuestions(response.data.itemList)
        
        // 设置选项
        if (response.data.itemList.length > 0 && response.data.itemList[0].itemOptionsList) {
          setOptions(response.data.itemList[0].itemOptionsList.map(option => ({
            value: option.optionsCode,
            text: option.optionsDesc
          })))
        } else {
          // 默认选项
          setOptions([
            { value: 'A', text: '完全没有' },
            { value: 'B', text: '有一点点' },
            { value: 'C', text: '还算不少' },
            { value: 'D', text: '非常的多' }
          ])
        }
      } else {
        showToast('获取题目失败')
      }
    } catch (error) {
      console.error('获取题目列表失败:', error)
      showToast('获取题目失败')
    } finally {
      setLoading(false)
    }
  }

  // 选择选项
  const selectOption = (option) => {
    setSelectedAnswer(option.value)
    
    setTimeout(() => {
      // 记录答案
      const currentItem = questions[currentQuestionIndex - 1]
      const itemSerNum = currentItem ? currentItem.itemSerNum : currentQuestionIndex
      const answerRecord = `${itemSerNum},${option.value}`
      
      const newAnswers = [...answers]
      if (newAnswers.length >= currentQuestionIndex) {
        newAnswers[currentQuestionIndex - 1] = answerRecord
      } else {
        newAnswers.push(answerRecord)
      }
      setAnswers(newAnswers)

      // 检查是否为最后一题
      if (currentQuestionIndex >= totalQuestions) {
        setShowSubmitModal(true)
        return
      }

      // 进入下一题
      setCurrentQuestionIndex(prev => prev + 1)
      setSelectedAnswer('')
    }, 250)
  }

  // 上一题
  const goToPrevious = () => {
    if (currentQuestionIndex > 1) {
      setCurrentQuestionIndex(prev => prev - 1)
      
      // 恢复上一题的答案
      const previousAnswer = answers[currentQuestionIndex - 2]
      if (previousAnswer) {
        setSelectedAnswer(previousAnswer.split(',')[1])
      }
      
      // 移除当前题的答案
      setAnswers(prev => prev.slice(0, -1))
    }
  }

  // 返回上一页
  const goBack = () => {
    navigate(-1)
  }

  // 确认提交
  const confirmSubmit = async () => {
    setIsSubmitting(true)
    try {
      const serNumList = []
      const answerList = []

      answers.forEach(answer => {
        const [serNum, answerCode] = answer.split(',')
        serNumList.push(parseInt(serNum))
        answerList.push(answerCode)
      })

      await subQuestionData({
        projectId,
        workorderId: workOrderId,
        traineeId,
        serNumList,
        answerList,
        evaluationSourceType: "0"
      })

      showToast('提交成功')
      // 返回到工单详情页面
      navigate('/assessment', { replace: true })
    } catch (error) {
      showToast(error.message || '提交失败')
    } finally {
      setIsSubmitting(false)
      setShowSubmitModal(false)
    }
  }

  // 取消提交
  const cancelSubmit = () => {
    setShowSubmitModal(false)
    setAnswers(prev => prev.slice(0, -1))
  }

  // 关闭指导语
  const closeGuide = () => {
    setShowGuideModal(false)
  }

  return (
    <div className="questionnaire-page">
      {/* 顶部导航栏 */}
      <div className="questionnaire-header">
        <div className="back-btn" onClick={goBack}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <div className="questionnaire-title">{projectName}</div>
      </div>

      {/* 进度条 */}
      <div className="progress-section">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${progressPercent}%` }}
          ></div>
        </div>
        <div className="progress-text">
          <span className="current-number">{currentQuestionIndex}</span>/{totalQuestions}
        </div>
      </div>

      {/* 题目内容 */}
      <div className="question-content">
        <div className="question-instruction">请根据您孩子过去一周内的实际情况选择</div>
        
        {loading ? (
          <div className="question-loading">
            <div>正在加载题目...</div>
          </div>
        ) : questions.length > 0 ? (
          <div className="question-container">
            <div className="question-text">
              {currentQuestion.itemContent || ''}
            </div>
            <div className="options-container">
              {options.map((option) => (
                <div
                  key={option.value}
                  className={`option-item ${selectedAnswer === option.value ? 'selected' : ''}`}
                  onClick={() => selectOption(option)}
                >
                  {option.text}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="question-empty">
            <div>暂无题目数据</div>
          </div>
        )}
      </div>

      {/* 底部按钮 */}
      {showPreviousButton && (
        <div className="bottom-section">
          <button className="previous-button" onClick={goToPrevious}>
            上一题
          </button>
        </div>
      )}

      {/* 指导语弹窗 */}
      {showGuideModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <div className="modal-title">指导语</div>
            </div>
            <div className="modal-body">
              0分一从来没有；<br />
              1分一有时(偶尔每月几次)；<br />
              2分一经常 (每周几次)；<br />
              3分一非常常见(每天一次或者多次，或者每周几天一天多次)。<br />
              如果评分人在1分与2分之间难以选择时，请选择1分。
            </div>
            <div className="modal-footer">
              <button className="modal-button-single" onClick={closeGuide}>
                我已知晓
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 提交确认弹窗 */}
      {showSubmitModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <div className="modal-title">确认提交问卷</div>
            </div>
            <div className="modal-body">
              本次问卷已完成，是否提交本次评估结果
            </div>
            <div className="modal-footer">
              <button className="modal-button-cancel" onClick={cancelSubmit} disabled={isSubmitting}>
                取消
              </button>
              <button className="modal-button-confirm" onClick={confirmSubmit} disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : '确认'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
