/* 测评介绍页面样式 */
.introduce-page {
  width: 100vw;
  height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 顶部导航栏 */
.introduce-header {
  position: relative;
  width: 100%;
  height: 82px;
  background: #ffffff;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn {
  position: absolute;
  left: 37px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.introduce-title {
  font-family: 'Alibaba PuHuiTi', 'PingFang SC', sans-serif;
  font-size: 30px;
  color: #333333;
  line-height: 30px;
  font-weight: 600;
}

/* 介绍文本区域 */
.introduce-description {
  width: 100%;
  padding: 0 24px;
  margin-top: 50px;
}

.main-text {
  width: 100%;
  font-family: 'PingFang SC', sans-serif;
  font-size: 28px;
  font-weight: bold;
  color: #111111;
  line-height: 40px;
  margin-bottom: 20px;
}

.detail-text {
  width: 100%;
  font-family: 'PingFang SC', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: #666666;
  line-height: 40px;
  text-align: left;
}

/* 统计卡片容器 */
.stats-container {
  width: 100%;
  max-width: 745px;
  height: 116px;
  display: flex;
  justify-content: space-between;
  margin-top: 35px;
  padding: 0 24px;
  gap: 16px;
}

.stats-card {
  background-color: #e9e9e9;
  border-radius: 8px;
  flex: 1;
  height: 116px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.stats-number {
  color: #111111;
  font-size: 40px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: bold;
  line-height: 40px;
  margin-bottom: 8px;
}

.stats-label {
  color: #666666;
  font-size: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: 20px;
}

/* 开始按钮容器 */
.start-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 90px;
  padding: 0 24px;
}

.start-button {
  width: 100%;
  max-width: 560px;
  height: 88px;
  background-color: #41a8de;
  border-radius: 44px;
  border: none;
  color: #ffffff;
  font-size: 40px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: 40px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.start-button:hover {
  background-color: #3a96c7;
}

.start-button:active {
  background-color: #3284b0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .introduce-description {
    padding: 0 16px;
    margin-top: 30px;
  }
  
  .main-text {
    font-size: 24px;
    line-height: 36px;
  }
  
  .detail-text {
    font-size: 20px;
    line-height: 32px;
  }
  
  .stats-container {
    padding: 0 16px;
    margin-top: 25px;
    gap: 12px;
  }
  
  .stats-card {
    height: 100px;
  }
  
  .stats-number {
    font-size: 32px;
    margin-bottom: 6px;
  }
  
  .stats-label {
    font-size: 18px;
  }
  
  .start-button-container {
    margin-top: 60px;
    padding: 0 16px;
  }
  
  .start-button {
    height: 80px;
    font-size: 36px;
  }
}

@media (max-width: 480px) {
  .introduce-title {
    font-size: 26px;
  }
  
  .main-text {
    font-size: 22px;
    line-height: 32px;
  }
  
  .detail-text {
    font-size: 18px;
    line-height: 28px;
  }
  
  .stats-container {
    flex-direction: column;
    height: auto;
    gap: 8px;
  }
  
  .stats-card {
    height: 80px;
    flex-direction: row;
    justify-content: space-around;
  }
  
  .stats-number {
    font-size: 28px;
    margin-bottom: 0;
    margin-right: 12px;
  }
  
  .stats-label {
    font-size: 16px;
  }
  
  .start-button {
    height: 72px;
    font-size: 32px;
  }
}
