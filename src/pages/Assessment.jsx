import { useEffect, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import '../App.css'
import { apiBaseUrl } from '../config/env'

function normalizeIdentity(identityRaw) {
  if (!identityRaw) return 'parent'
  const value = decodeURIComponent(identityRaw).toLowerCase()
  if (value === 'parents') return 'parent'
  if (value === 'teacher' || value === '教师') return 'teacher'
  if (value === 'parent' || value === '家长') return 'parent'
  return 'parent'
}

export default function Assessment() {
  const [searchParams] = useSearchParams()
  const workOrderId = searchParams.get('workOrderId') || ''
  const traineeId = searchParams.get('traineeId') || ''
  const identity = useMemo(() => normalizeIdentity(searchParams.get('identity')), [searchParams])

  const isTeacherMode = identity === 'teacher'
  const [activeTab, setActiveTab] = useState(isTeacherMode ? 'teacher' : 'parent')

  // 如果是教师身份，强制为教师 Tab
  const effectiveTab = isTeacherMode ? 'teacher' : activeTab

  // 数据状态
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [workOrderData, setWorkOrderData] = useState(null)

  useEffect(() => {
    if (!workOrderId || !apiBaseUrl) {
      return
    }
    let aborted = false
    setLoading(true)
    setError('')
    fetch(`${apiBaseUrl}workorder/v1.1/qryWorkOrderDetailInfo`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', sourceType: 'H5' },
      body: JSON.stringify({ workOrderId, traineeId }),
    })
      .then(async (res) => {
        const data = await res.json().catch(() => ({}))
        if (aborted) return
        if (data && data.code === '0000') {
          setWorkOrderData(data.data || null)
        } else {
          setError(data?.msg || '请求失败')
        }
      })
      .catch(() => {
        if (aborted) return
        setError('网络异常')
      })
      .finally(() => {
        if (aborted) return
        setLoading(false)
      })
    return () => {
      aborted = true
    }
  }, [workOrderId, traineeId])

  const respondentGroups = workOrderData?.respondentGroupInfoList || []
  const parentGroup = useMemo(
    () => respondentGroups.find((g) => g.respondentGroupType === 'P'),
    [respondentGroups]
  )
  const teacherGroup = useMemo(
    () => respondentGroups.find((g) => g.respondentGroupType === 'T'),
    [respondentGroups]
  )

  const parentProcess = parentGroup?.process || '1/3'
  const teacherProcess = teacherGroup?.process || '0/2'

  const traineeSimDesc = workOrderData?.traineeSimDesc
  const roleLabel = isTeacherMode ? '教师' : '家长'

  return (
    <div className="app">
      {/* 标题栏 */}
      <div className="header">
        <div className="header-background"></div>
        <div className="header-title">测评项目</div>
      </div>

      {/* 顶部标签栏：仅家长身份显示 Tab 切换 */
      }
      {!isTeacherMode && (
        <div className="top-tabs">
          <div className={`tab ${effectiveTab === 'parent' ? 'active' : ''}`} onClick={() => setActiveTab('parent')}>
            <span>家长部分（{parentProcess}）</span>
          </div>
          <div className={`tab ${effectiveTab === 'teacher' ? 'active' : ''}`} onClick={() => setActiveTab('teacher')}>
            <span>教师部分（{teacherProcess}）</span>
          </div>
          <div className={`tab-indicator ${effectiveTab === 'teacher' ? 'second' : ''}`}></div>
        </div>
      )}

      {/* 顶部资料信息 */}
      <div className="info-section">
        <div className="hospital-info">浙江xxxxxx医院神经内科:</div>
        <div className="patient-info">
          {traineeSimDesc
            ? `请${traineeSimDesc}${roleLabel}完成以下测评项目...`
            : `请张小明${roleLabel}完成以下测评项目...`}
        </div>
        <div className="validity-info">有效期至2025年6月10日</div>
      </div>

      {/* 内容区域：根据标签或身份切换 */}
      {loading && (
        <div className="assessment-list" style={{ padding: '0 24px', color: '#666' }}>加载中...</div>
      )}
      {!loading && error && (
        <div className="assessment-list" style={{ padding: '0 24px', color: '#ff4d4f' }}>{error}</div>
      )}
      {effectiveTab === 'parent' ? (
        <>
          {/* 测评项目列表（家长） */}
          <div className="assessment-list">
            {(parentGroup?.detailProjectInfoList || []).length > 0 ? (
              (parentGroup?.detailProjectInfoList || []).map((item, idx) => {
                const isDone = item?.state === '1'
                return isDone ? (
                  <div className="assessment-item completed" key={`p-done-${idx}`}>
                    <div className="completed-badge">
                      <svg width="15" height="10" viewBox="0 0 15 10" fill="none">
                        <path d="M1 5L5 9L14 1" stroke="#287FFF" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <div className="assessment-content">
                      <div className="assessment-title">
                        {item?.projectName || '—'}
                      </div>
                      <div className="assessment-buttons">
                        <button className="detail-btn">详情</button>
                        <button className="retest-btn">重新测评</button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="assessment-item" key={`p-todo-${idx}`}>
                    <div className="assessment-content">
                      <div className="assessment-title">
                        {item?.projectName || '—'}
                      </div>
                      <div className="assessment-buttons">
                        <button className="start-btn">开始测评</button>
                      </div>
                    </div>
                  </div>
                )
              })
            ) : (
              <>
                {/* 无数据则保留演示项 */}
                <div className="assessment-item completed">
                  <div className="completed-badge">
                    <svg width="15" height="10" viewBox="0 0 15 10" fill="none">
                      <path d="M1 5L5 9L14 1" stroke="#287FFF" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="assessment-content">
                    <div className="assessment-title">
                      SNAP-IV父母评定量表<br/>(26项)
                    </div>
                    <div className="assessment-buttons">
                      <button className="detail-btn">详情</button>
                      <button className="retest-btn">重新测评</button>
                    </div>
                  </div>
                </div>
                <div className="assessment-item">
                  <div className="assessment-content">
                    <div className="assessment-title">
                      Vanderbilt ADHD诊断评定量表<br/>(VADRS) (家长问卷)
                    </div>
                    <div className="assessment-buttons">
                      <button className="start-btn">开始测评</button>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      ) : (
        <>
          {/* 测评项目列表（教师） */}
          <div className="assessment-list">
            {(teacherGroup?.detailProjectInfoList || []).length > 0 ? (
              (teacherGroup?.detailProjectInfoList || []).map((item, idx) => {
                const isDone = item?.state === '1'
                return (
                  <div className={`assessment-item ${isDone ? 'completed' : ''}`} key={`t-${idx}`}>
                    <div className="assessment-content">
                      <div className="assessment-title">
                        {item?.projectName || '—'}
                      </div>
                    <div className="assessment-buttons">
                      {isDone ? (
                        <button className="badge-done">已完成</button>
                      ) : (
                        <button className="start-btn">开始测评</button>
                      )}
                    </div>
                    </div>
                  </div>
                )
              })
            ) : (
              <>
                {/* 无数据则保留演示项 */}
                <div className="assessment-item completed">
                  <div className="assessment-content">
                    <div className="assessment-title">
                      长处和困难问卷(SDO)<br/>(老师版)
                    </div>
                    <div className="assessment-buttons">
                      <button className="badge-done">已完成</button>
                    </div>
                  </div>
                </div>
                <div className="assessment-item">
                  <div className="assessment-content">
                    <div className="assessment-title">
                      Rutter儿童行为问卷<br/>(教师问卷)
                    </div>
                    <div className="assessment-buttons">
                      <button className="start-btn">开始测评</button>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      )}

      {/* 底部提示 */}
      <div className="bottom-tip">项目完成后，关闭页面即可</div>
    </div>
  )
}


