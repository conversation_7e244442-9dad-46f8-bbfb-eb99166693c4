import { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { getQuestionItemList } from '../services/scale'
import './AssessmentIntroduce.css'

export default function AssessmentIntroduce() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  
  // 获取URL参数
  const projectId = searchParams.get('projectId') || ''
  const projectName = searchParams.get('projectName') || 'SNAP-IV父母评定问卷'
  const workOrderId = searchParams.get('workOrderId') || ''
  const traineeId = searchParams.get('traineeId') || ''
  
  // 状态管理
  const [questionCount, setQuestionCount] = useState(26) // 默认26题
  
  // 统计数据
  const statsData = [
    {
      number: '3',
      label: '行为维度(个)'
    },
    {
      number: questionCount.toString(),
      label: '题目数量(题)'
    },
    {
      number: '5',
      label: '测评耗时(分钟)'
    }
  ]

  // 获取题目数量
  useEffect(() => {
    if (projectId) {
      fetchQuestionCount()
    }
  }, [projectId])

  const fetchQuestionCount = async () => {
    try {
      const response = await getQuestionItemList({ projectId })
      if (response && response.code === '0000' && response.data && Array.isArray(response.data.itemList)) {
        const count = response.data.itemList.length || 0
        setQuestionCount(count)
      }
    } catch (error) {
      console.error('获取题目数量失败:', error)
    }
  }

  // 返回上一页
  const goBack = () => {
    navigate(-1)
  }

  // 测试API调用
  const testApiCall = async () => {
    console.log('Testing API call with projectId:', projectId)
    try {
      const response = await getQuestionItemList({ projectId: projectId || 'test-project' })
      console.log('API Response:', response)
      alert(`API调用成功！响应: ${JSON.stringify(response, null, 2)}`)
    } catch (error) {
      console.error('API调用失败:', error)
      alert(`API调用失败: ${error.message}`)
    }
  }

  // 开始测评
  const startAssessment = () => {
    const params = new URLSearchParams({
      projectId,
      projectName,
      workOrderId,
      traineeId
    })
    navigate(`/assessment-questionnaire?${params.toString()}`)
  }

  return (
    <div className="introduce-page">
      {/* 顶部导航栏 */}
      <div className="introduce-header">
        <div className="back-btn" onClick={goBack}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <div className="introduce-title">{projectName}</div>
      </div>

      {/* 介绍文本 */}
      <div className="introduce-description">
        <div className="main-text">
          为了更全面地了解您孩子专注与多动的情况，您需要填写SNAP-IV问卷的信息。
        </div>
        <div className="detail-text">
          SNAP-IV量表由Swanson、Nolan和Pelham等根据DSM(《美国精神障碍诊断与统计手册》)
          <br />
          中关于儿童注意缺陷多动障碍(ADHD)的诊断标准编制而成，因其项目与DSM-IV诊断项目直
          <br />
          接对应，因此临床使用的针对性较强，常被各大医院所使用。
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="stats-container">
        {statsData.map((item, index) => (
          <div key={index} className="stats-card">
            <div className="stats-number">{item.number}</div>
            <div className="stats-label">{item.label}</div>
          </div>
        ))}
      </div>

      {/* 开始填写按钮 */}
      <div className="start-button-container">
        <button className="start-button" onClick={startAssessment}>
          开始填写
        </button>
        <button className="start-button" onClick={testApiCall} style={{marginTop: '10px', backgroundColor: '#ff6b6b'}}>
          测试API调用
        </button>
      </div>
    </div>
  )
}
