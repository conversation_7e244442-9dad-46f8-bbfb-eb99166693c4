import { Link } from 'react-router-dom'

export default function Guide() {
  return (
    <div style={{ padding: 24 }}>
      <h2>测评入口</h2>
      <p>请通过带有参数的链接进入测评页面。</p>
      <p>示例：</p>
      <ul>
        <li>
          <Link to="/assessment?workOrderId=940680688171094016&identity=家长&traineeId=8888">/assessment?workOrderId=940680688171094016&identity=家长&traineeId=8888</Link>
        </li>
        <li>
          <Link to="/assessment?workOrderId=940680688171094016&identity=教师&traineeId=8888">/assessment?workOrderId=940680688171094016&identity=教师&traineeId=8888</Link>
        </li>
      </ul>
    </div>
  )
}


