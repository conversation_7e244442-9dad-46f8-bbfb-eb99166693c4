# 测评功能使用说明

## 功能概述

本项目已成功实现了点击开始测评的UI界面和功能，包括：

1. **测评介绍页面** (`/assessment-introduce`) - 展示测评项目信息和统计数据
2. **测评问卷页面** (`/assessment-questionnaire`) - 实际的问卷填写界面
3. **API服务** - 获取题目列表和提交答案的接口
4. **路由配置** - 支持页面间的导航

## 页面流程

```
Assessment页面 → 点击"开始测评" → 测评介绍页面 → 点击"开始填写" → 测评问卷页面 → 完成提交
```

## 使用方法

### 1. 访问测评页面

访问以下URL来进入测评系统：

```
http://127.0.0.1:5174/assessment?workOrderId=940680688171094016&identity=家长&traineeId=8888
```

参数说明：
- `workOrderId`: 工单ID
- `identity`: 身份（家长/教师）
- `traineeId`: 患者ID

### 2. 开始测评

在Assessment页面中，点击任意测评项目的"开始测评"按钮，系统会：
1. 自动跳转到测评介绍页面
2. 传递项目相关参数（projectId, projectName, workOrderId, traineeId）

### 3. 查看测评介绍

测评介绍页面包含：
- 项目名称和描述
- 统计信息卡片（行为维度、题目数量、测评耗时）
- "开始填写"按钮

### 4. 填写问卷

点击"开始填写"后进入问卷页面，包含：
- 进度条显示当前进度
- 题目内容展示
- 选项选择（支持点击选择）
- 上一题按钮（第一题除外）
- 指导语弹窗（首次进入显示）
- 提交确认弹窗（最后一题完成后显示）

## 技术实现

### 新增文件

1. **API服务** (`src/services/scale.js`)
   - `getQuestionItemList()` - 获取题目列表
   - `subQuestionData()` - 提交问卷数据
   - `showToast()` - 显示提示信息

2. **测评介绍页面** (`src/pages/AssessmentIntroduce.jsx`)
   - 项目信息展示
   - 统计卡片组件
   - 导航功能

3. **测评问卷页面** (`src/pages/AssessmentQuestionnaire.jsx`)
   - 问卷逻辑处理
   - 进度管理
   - 答案记录和提交

4. **样式文件**
   - `src/pages/AssessmentIntroduce.css`
   - `src/pages/AssessmentQuestionnaire.css`

### 修改文件

1. **路由配置** (`src/App.jsx`)
   - 添加新页面路由
   - 导入新组件

2. **Assessment页面** (`src/pages/Assessment.jsx`)
   - 添加开始测评点击事件
   - 实现页面导航功能

## API接口

### 获取题目列表

```javascript
POST /scale/v1.1/getQuestionItemList
Content-Type: application/json
sourceType: H5

{
  "projectId": "项目ID"
}
```

### 提交问卷数据

```javascript
POST /scale/v1.1/subQuestionData
Content-Type: application/json
sourceType: H5

{
  "projectId": "项目ID",
  "workorderId": "工单ID", 
  "traineeId": "患者ID",
  "serNumList": [1, 2, 3],
  "answerList": ["A", "B", "C"],
  "evaluationSourceType": "0"
}
```

## 响应式设计

所有页面都支持响应式设计，适配：
- 桌面端 (>768px)
- 平板端 (768px-480px)
- 移动端 (<480px)

## 注意事项

1. 确保API基础URL在环境变量中正确配置
2. 题目数据从API动态获取，支持不同项目的不同题目数量
3. 答案提交采用序号+选项代码的格式
4. 支持上一题功能，可以修改之前的答案
5. 提交前会显示确认弹窗，避免误操作

## 测试建议

建议编写单元测试来验证：
1. 页面导航功能
2. API调用逻辑
3. 答案记录和提交
4. 响应式布局
5. 用户交互流程
